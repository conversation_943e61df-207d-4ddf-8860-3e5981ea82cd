# Database Configuration
MONGODB_URI=mongodb+srv://username:<EMAIL>/autoreply

# JWT Secret (Generate a strong random string)
JWT_SECRET=your-super-secret-jwt-key-here-make-it-long-and-random

# Session Secret
SESSION_SECRET=your-session-secret-key-here

# Google Gemini AI Configuration
GEMINI_API_KEY=your-gemini-api-key-from-google-ai-studio

# Facebook Integration (Optional)
FACEBOOK_APP_ID=your-facebook-app-id
FACEBOOK_APP_SECRET=your-facebook-app-secret
FACEBOOK_VERIFY_TOKEN=your-webhook-verify-token

# Server Configuration
PORT=3000
NODE_ENV=production

# Frontend URL (for CORS in production)
FRONTEND_URL=https://your-domain.com

# Additional Security Settings
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Logging Level
LOG_LEVEL=info
