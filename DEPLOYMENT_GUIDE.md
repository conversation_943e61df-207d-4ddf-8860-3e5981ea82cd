# دليل النشر - AutoReply Application

## نظرة عامة

هذا الدليل يوضح كيفية نشر تطبيق AutoReply على خدمات الاستضافة المختلفة مع التركيز على Firebase Hosting.

## المتطلبات الأساسية

### 1. إعد<PERSON> قاعدة البيانات
```bash
# MongoDB Atlas (مُوصى به للإنتاج)
1. إنشاء حساب على https://cloud.mongodb.com
2. إنشاء cluster جديد
3. إعداد Database User
4. إعداد Network Access (IP Whitelist)
5. الحصول على Connection String
```

### 2. إعداد Google Gemini AI
```bash
# الحصول على API Key
1. زيارة https://makersuite.google.com/app/apikey
2. إنشاء API Key جديد
3. حفظ المفتاح بشكل آمن
```

### 3. إعداد Facebook Integration (اختياري)
```bash
# إنشاء Facebook App
1. زيارة https://developers.facebook.com
2. إنشاء تطبيق جديد
3. إضافة Messenger Platform
4. الحصول على App ID و App Secret
5. إعداد Webhook
```

## النشر على Firebase

### 1. إعداد Firebase
```bash
# تثبيت Firebase CLI
npm install -g firebase-tools

# تسجيل الدخول
firebase login

# تهيئة المشروع
firebase init
```

### 2. إعداد ملفات Firebase
إنشاء `firebase.json`:
```json
{
  "hosting": {
    "public": "public",
    "ignore": [
      "firebase.json",
      "**/.*",
      "**/node_modules/**"
    ],
    "rewrites": [
      {
        "source": "/api/**",
        "function": "api"
      },
      {
        "source": "**",
        "destination": "/index.html"
      }
    ]
  },
  "functions": {
    "source": ".",
    "runtime": "nodejs18"
  }
}
```

إنشاء `.firebaserc`:
```json
{
  "projects": {
    "default": "your-project-id"
  }
}
```

### 3. إعداد متغيرات البيئة
```bash
# إعداد متغيرات Firebase Functions
firebase functions:config:set mongodb.uri="your-mongodb-uri"
firebase functions:config:set jwt.secret="your-jwt-secret"
firebase functions:config:set gemini.api_key="your-gemini-key"
firebase functions:config:set facebook.app_id="your-app-id"
firebase functions:config:set facebook.app_secret="your-app-secret"
```

### 4. تعديل server.js للـ Firebase Functions
إنشاء `index.js` في المجلد الجذر:
```javascript
const functions = require('firebase-functions');
const express = require('express');
const app = require('./server');

exports.api = functions.https.onRequest(app);
```

### 5. النشر
```bash
# النشر الكامل
firebase deploy

# نشر الـ hosting فقط
firebase deploy --only hosting

# نشر الـ functions فقط
firebase deploy --only functions
```

## النشر على Heroku

### 1. إعداد Heroku
```bash
# تثبيت Heroku CLI
# تسجيل الدخول
heroku login

# إنشاء تطبيق جديد
heroku create your-app-name
```

### 2. إعداد متغيرات البيئة
```bash
heroku config:set MONGODB_URI="your-mongodb-uri"
heroku config:set JWT_SECRET="your-jwt-secret"
heroku config:set GEMINI_API_KEY="your-gemini-key"
heroku config:set FACEBOOK_APP_ID="your-app-id"
heroku config:set FACEBOOK_APP_SECRET="your-app-secret"
heroku config:set NODE_ENV="production"
```

### 3. إعداد Procfile
إنشاء ملف `Procfile`:
```
web: node server.js
```

### 4. النشر
```bash
# إضافة remote
heroku git:remote -a your-app-name

# النشر
git push heroku main
```

## النشر على VPS

### 1. إعداد الخادم
```bash
# تحديث النظام
sudo apt update && sudo apt upgrade -y

# تثبيت Node.js
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# تثبيت MongoDB (اختياري)
sudo apt-get install -y mongodb

# تثبيت Nginx
sudo apt-get install -y nginx

# تثبيت PM2
sudo npm install -g pm2
```

### 2. رفع الملفات
```bash
# استخدام Git
git clone https://github.com/yourusername/autoreply.git
cd autoreply

# أو استخدام SCP
scp -r ./autoreply user@server:/var/www/
```

### 3. إعداد التطبيق
```bash
# تثبيت المتطلبات
npm install --production

# إنشاء ملف .env
nano .env
```

### 4. إعداد PM2
```bash
# تشغيل التطبيق
pm2 start server.js --name autoreply

# حفظ الإعدادات
pm2 startup
pm2 save

# مراقبة التطبيق
pm2 monit
```

### 5. إعداد Nginx
إنشاء `/etc/nginx/sites-available/autoreply`:
```nginx
server {
    listen 80;
    server_name your-domain.com;

    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
}
```

```bash
# تفعيل الموقع
sudo ln -s /etc/nginx/sites-available/autoreply /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl restart nginx
```

## إعداد HTTPS

### استخدام Let's Encrypt
```bash
# تثبيت Certbot
sudo apt-get install -y certbot python3-certbot-nginx

# الحصول على شهادة SSL
sudo certbot --nginx -d your-domain.com

# تجديد تلقائي
sudo crontab -e
# إضافة: 0 12 * * * /usr/bin/certbot renew --quiet
```

## مراقبة التطبيق

### 1. مراقبة PM2
```bash
# عرض الحالة
pm2 status

# عرض السجلات
pm2 logs autoreply

# إعادة تشغيل
pm2 restart autoreply

# إيقاف
pm2 stop autoreply
```

### 2. مراقبة قاعدة البيانات
```bash
# MongoDB Atlas Monitoring
# استخدام لوحة التحكم الخاصة بـ Atlas

# MongoDB محلي
mongo
> db.stats()
> db.orders.count()
> db.products.count()
```

## النسخ الاحتياطي

### 1. نسخ احتياطي لقاعدة البيانات
```bash
# MongoDB Atlas
# استخدام خدمة النسخ الاحتياطي المدمجة

# MongoDB محلي
mongodump --db autoreply --out /backup/$(date +%Y%m%d)
```

### 2. نسخ احتياطي للملفات
```bash
# إنشاء نسخة احتياطية
tar -czf autoreply-backup-$(date +%Y%m%d).tar.gz /var/www/autoreply

# رفع للتخزين السحابي
# استخدام AWS S3, Google Cloud Storage, أو Dropbox
```

## استكشاف الأخطاء

### 1. مشاكل شائعة
```bash
# خطأ في الاتصال بقاعدة البيانات
# تحقق من MONGODB_URI في .env

# خطأ في المنفذ
# تحقق من PORT في .env أو استخدام المنفذ الافتراضي

# خطأ في المصادقة
# تحقق من JWT_SECRET

# خطأ في Gemini AI
# تحقق من GEMINI_API_KEY
```

### 2. فحص السجلات
```bash
# PM2 logs
pm2 logs autoreply --lines 100

# Nginx logs
sudo tail -f /var/log/nginx/error.log
sudo tail -f /var/log/nginx/access.log

# System logs
sudo journalctl -u nginx -f
```

## الأمان في الإنتاج

### 1. إعدادات الأمان
```bash
# تحديث كلمات المرور
# تغيير JWT_SECRET
# استخدام HTTPS فقط
# تفعيل Firewall
sudo ufw enable
sudo ufw allow ssh
sudo ufw allow 'Nginx Full'
```

### 2. مراقبة الأمان
```bash
# مراقبة محاولات الدخول
sudo tail -f /var/log/auth.log

# تحديث النظام بانتظام
sudo apt update && sudo apt upgrade

# مراقبة استخدام الموارد
htop
df -h
free -h
```

## الصيانة

### 1. تحديثات دورية
```bash
# تحديث التطبيق
git pull origin main
npm install
pm2 restart autoreply

# تحديث النظام
sudo apt update && sudo apt upgrade
```

### 2. تنظيف السجلات
```bash
# تنظيف سجلات PM2
pm2 flush

# تنظيف سجلات النظام
sudo journalctl --vacuum-time=7d
```

---

**ملاحظة**: تأكد من اختبار جميع الوظائف بعد النشر والاحتفاظ بنسخ احتياطية منتظمة.
