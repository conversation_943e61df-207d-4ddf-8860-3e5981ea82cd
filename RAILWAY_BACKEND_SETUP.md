# إعداد الخادم الخلفي على Railway

## الخطوات المطلوبة

### 1. إنشاء مجلد منفصل للخادم الخلفي
```bash
mkdir autoreply-backend
cd autoreply-backend
```

### 2. نسخ الملفات المطلوبة
```bash
# نسخ ملفات الخادم
cp ../backend-package.json package.json
cp ../backend-server.js server.js
cp ../backend.env .env

# نسخ المجلدات المطلوبة
cp -r ../config .
cp -r ../models .
cp -r ../routes .
cp -r ../utils .
```

### 3. إنشاء ملف .gitignore
```bash
# Dependencies
node_modules/
npm-debug.log*

# Environment variables
.env
.env.local
.env.production.local

# Logs
logs
*.log

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory
coverage/

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env.test

# Temporary folders
tmp/
temp/

# Editor directories and files
.vscode/
.idea/
*.swp
*.swo

# OS generated files
.DS_Store
Thumbs.db

# Data files
data/
```

### 4. تهيئة Git repository
```bash
git init
git add .
git commit -m "Initial commit: AutoReply Backend"
```

### 5. رفع إلى GitHub
```bash
# إنشاء repository جديد على GitHub باسم: autoreply-backend
git branch -M main
git remote add origin https://github.com/yourusername/autoreply-backend.git
git push -u origin main
```

### 6. نشر على Railway
```bash
# زيارة https://railway.app
# تسجيل الدخول بـ GitHub
# اختيار "Deploy from GitHub repo"
# اختيار repository: autoreply-backend
# انتظار النشر التلقائي
```

### 7. إعداد متغيرات البيئة في Railway
```bash
# في لوحة تحكم Railway > Variables:
MONGODB_URI=mongodb+srv://yousefmuhamedeng22:<EMAIL>/autoreply
JWT_SECRET=AutoReply2025SecureJWTKeyForProductionUseOnly!@#$%^&*()
SESSION_SECRET=AutoReply2025SecureSessionKeyForProductionUseOnly!@#$%^&*()
GEMINI_API_KEY=AIzaSyAAPHM_8GBo0ZjY4XWscg-JU6LoSjguHPo
FACEBOOK_APP_ID=708787151686025
FACEBOOK_APP_SECRET=********************************
NODE_ENV=production
PORT=3000
FRONTEND_URL=https://elzaeemreblay-1fea2.web.app
```

### 8. الحصول على رابط Railway
```bash
# بعد النشر الناجح، ستحصل على رابط مثل:
# https://autoreply-backend-production.up.railway.app
```

### 9. تحديث رابط API في Firebase
```javascript
// تحديث public/js/api.js
baseUrl: window.location.hostname === 'localhost' ? '/api' : 'https://your-railway-url.railway.app/api'
```

### 10. إعادة نشر Firebase
```bash
firebase deploy --only hosting
```

## اختبار النشر

### 1. اختبار Health Check
```bash
curl https://your-railway-url.railway.app/health
```

### 2. اختبار API
```bash
curl https://your-railway-url.railway.app/api/products
```

### 3. اختبار التطبيق الكامل
```bash
# زيارة https://elzaeemreblay-1fea2.web.app
# تسجيل الدخول واختبار جميع الوظائف
```

## الملفات المطلوبة للخادم الخلفي

### package.json
```json
{
  "name": "autoreply-backend",
  "version": "1.0.0",
  "description": "AutoReply Backend API Server",
  "main": "server.js",
  "scripts": {
    "start": "node server.js",
    "dev": "nodemon server.js"
  },
  "dependencies": {
    "axios": "^1.6.2",
    "bcryptjs": "^2.4.3",
    "body-parser": "^1.20.2",
    "cors": "^2.8.5",
    "dotenv": "^16.3.1",
    "express": "^4.18.2",
    "express-session": "^1.17.3",
    "fs-extra": "^11.1.1",
    "jsonwebtoken": "^9.0.2",
    "moment": "^2.29.4",
    "mongoose": "^8.15.1",
    "uuid": "^9.0.1"
  }
}
```

### server.js
```javascript
require('dotenv').config();
const express = require('express');
const cors = require('cors');
// ... (الكود الكامل في backend-server.js)
```

### .env
```bash
MONGODB_URI=mongodb+srv://yousefmuhamedeng22:<EMAIL>/autoreply
JWT_SECRET=AutoReply2025SecureJWTKeyForProductionUseOnly!@#$%^&*()
# ... (المتغيرات الكاملة في backend.env)
```

## النتيجة المتوقعة

بعد اتباع هذه الخطوات، ستحصل على:

1. **خادم خلفي على Railway**: https://your-app.railway.app
2. **واجهة أمامية على Firebase**: https://elzaeemreblay-1fea2.web.app
3. **تطبيق كامل يعمل على الإنترنت**
4. **جميع الوظائف تعمل بشكل صحيح**
5. **مجاني بالكامل**

---

*ملاحظة: تأكد من استبدال "your-railway-url" بالرابط الفعلي الذي تحصل عليه من Railway*
