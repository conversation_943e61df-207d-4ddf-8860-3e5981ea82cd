require('dotenv').config();
const express = require('express');
const cors = require('cors');
const bodyParser = require('body-parser');
const session = require('express-session');
const mongoose = require('mongoose');

const app = express();
const PORT = process.env.PORT || 3000;

// Database connection
const connectDB = async () => {
  try {
    const mongoUri = process.env.MONGODB_URI;
    if (!mongoUri) {
      console.log('MONGODB_URI not found, running without database');
      return;
    }

    await mongoose.connect(mongoUri, {
      maxPoolSize: 10,
      serverSelectionTimeoutMS: 5000,
      socketTimeoutMS: 45000,
    });

    console.log('MongoDB connected successfully');
  } catch (error) {
    console.error('MongoDB connection failed:', error.message);
  }
};

// Connect to database (non-blocking)
connectDB();

// CORS Configuration
const corsOptions = {
  origin: [
    'https://elzaeemreblay-1fea2.web.app',
    'https://elzaeemreblay-1fea2.firebaseapp.com',
    'http://localhost:3000',
    'http://127.0.0.1:3000'
  ],
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'x-auth-token']
};

app.use(cors(corsOptions));

// Middleware
app.use(bodyParser.json({ limit: '10mb' }));
app.use(bodyParser.urlencoded({ extended: true, limit: '10mb' }));

app.use(session({
  secret: process.env.SESSION_SECRET || 'autoreply-secret-key',
  resave: false,
  saveUninitialized: true,
  cookie: {
    secure: process.env.NODE_ENV === 'production',
    httpOnly: true,
    maxAge: 24 * 60 * 60 * 1000 // 24 hours
  }
}));

// Health check endpoint
app.get('/health', (req, res) => {
  const dbStatus = mongoose.connection.readyState === 1 ? 'connected' : 'disconnected';
  res.json({
    status: 'OK',
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV || 'development',
    database: dbStatus,
    version: '1.0.0'
  });
});

// Root endpoint
app.get('/', (req, res) => {
  res.json({
    message: 'AutoReply Backend API Server',
    version: '1.0.0',
    status: 'Running',
    endpoints: {
      health: '/health',
      auth: '/api/auth',
      products: '/api/products',
      orders: '/api/orders',
      messages: '/api/messages',
      store: '/api/store',
      admin: '/api/admin',
      oauth: '/api/oauth',
      webhook: '/api/webhook'
    }
  });
});

// Basic API routes for testing
app.get('/api/test', (req, res) => {
  res.json({ message: 'API is working!', timestamp: new Date().toISOString() });
});

// Auth routes
app.post('/api/auth/register', (req, res) => {
  res.json({ message: 'Registration endpoint', success: true });
});

app.post('/api/auth/login', (req, res) => {
  res.json({ message: 'Login endpoint', success: true });
});

// Products routes
app.get('/api/products', (req, res) => {
  res.json({ message: 'Products list', products: [] });
});

app.post('/api/products', (req, res) => {
  res.json({ message: 'Product created', success: true });
});

// Orders routes
app.get('/api/orders', (req, res) => {
  res.json({ message: 'Orders list', orders: [] });
});

app.post('/api/orders', (req, res) => {
  res.json({ message: 'Order created', success: true });
});

// Store routes
app.get('/api/store/info', (req, res) => {
  res.json({ message: 'Store info', store: {} });
});

// Admin routes
app.post('/api/admin/activate', (req, res) => {
  res.json({ message: 'Activation endpoint', success: true });
});

// Messages routes
app.get('/api/messages', (req, res) => {
  res.json({ message: 'Messages list', messages: [] });
});

app.post('/api/messages', (req, res) => {
  res.json({ message: 'Message sent', success: true });
});

// OAuth routes
app.get('/api/oauth/facebook', (req, res) => {
  res.json({ message: 'Facebook OAuth endpoint' });
});

// Webhook routes
app.post('/api/webhook/facebook', (req, res) => {
  res.json({ message: 'Facebook webhook endpoint' });
});

// Error handling middleware
app.use((err, req, res, next) => {
  console.error('Error:', err);
  res.status(500).json({
    message: 'خطأ في الخادم',
    error: process.env.NODE_ENV === 'development' ? err.message : 'Internal Server Error'
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({ message: 'API endpoint not found' });
});

// Start server
app.listen(PORT, () => {
  console.log(`AutoReply Backend API Server running on port ${PORT}`);
  console.log(`Environment: ${process.env.NODE_ENV || 'development'}`);
  console.log(`Health check: http://localhost:${PORT}/health`);
  console.log(`API Documentation: http://localhost:${PORT}/`);
});

module.exports = app;
