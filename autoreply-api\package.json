{"name": "autoreply-api", "version": "1.0.0", "description": "AutoReply Backend API Server", "main": "index.js", "scripts": {"start": "node index.js", "dev": "nodemon index.js", "build": "echo 'Build completed'"}, "keywords": ["autoreply", "api", "backend", "ecommerce"], "author": "AutoReply Team", "license": "MIT", "engines": {"node": ">=16.0.0"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "dotenv": "^16.3.1", "mongoose": "^8.15.1", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "express-session": "^1.17.3", "body-parser": "^1.20.2", "axios": "^1.6.2", "moment": "^2.29.4", "uuid": "^9.0.1"}}