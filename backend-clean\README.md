# AutoReply Backend API Server

Backend API server for AutoReply - Smart E-commerce Customer Service Automation System with Arabic Support.

## Features

- 🤖 AI-powered customer service automation
- 🛒 E-commerce integration
- 📱 Facebook & Instagram messaging
- 🇸🇦 Arabic language support
- 🔐 Secure authentication
- 📊 Order and product management

## API Endpoints

### Health Check
- `GET /health` - Server health status

### Authentication
- `POST /api/auth/register` - User registration
- `POST /api/auth/login` - User login
- `POST /api/auth/logout` - User logout

### Products
- `GET /api/products` - Get all products
- `POST /api/products` - Create new product
- `PUT /api/products/:id` - Update product
- `DELETE /api/products/:id` - Delete product

### Orders
- `GET /api/orders` - Get all orders
- `POST /api/orders` - Create new order
- `PUT /api/orders/:id` - Update order
- `DELETE /api/orders/:id` - Delete order

### Messages
- `GET /api/messages` - Get all messages
- `POST /api/messages` - Send message

### Store
- `GET /api/store/info` - Get store information
- `PUT /api/store/info` - Update store information

### Admin
- `POST /api/admin/activate` - Activate user account
- `GET /api/admin/stats` - Get system statistics

### OAuth
- `GET /api/oauth/facebook` - Facebook OAuth
- `GET /api/oauth/callback` - OAuth callback

### Webhook
- `POST /api/webhook/facebook` - Facebook webhook

## Environment Variables

```env
MONGODB_URI=mongodb+srv://...
JWT_SECRET=your-jwt-secret
SESSION_SECRET=your-session-secret
GEMINI_API_KEY=your-gemini-api-key
FACEBOOK_APP_ID=your-facebook-app-id
FACEBOOK_APP_SECRET=your-facebook-app-secret
NODE_ENV=production
PORT=3000
```

## Deployment

This backend is designed to be deployed on Railway.app with MongoDB Atlas.

### Railway Deployment

1. Connect your GitHub repository to Railway
2. Set environment variables in Railway dashboard
3. Deploy automatically

### MongoDB Atlas

1. Create MongoDB Atlas cluster
2. Add connection string to MONGODB_URI
3. Configure network access (allow all IPs for Railway)

## License

MIT License
