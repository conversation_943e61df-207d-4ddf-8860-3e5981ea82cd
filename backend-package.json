{"name": "autoreply-backend", "version": "1.0.0", "description": "AutoReply Backend API Server", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "build": "echo 'Build completed'", "test": "echo 'No tests specified'"}, "keywords": ["autoreply", "ecommerce", "chatbot", "arabic", "api", "backend"], "author": "AutoReply Team", "license": "MIT", "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}, "dependencies": {"axios": "^1.6.2", "bcryptjs": "^2.4.3", "body-parser": "^1.20.2", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-session": "^1.17.3", "fs-extra": "^11.1.1", "jsonwebtoken": "^9.0.2", "moment": "^2.29.4", "mongoose": "^8.15.1", "uuid": "^9.0.1"}, "devDependencies": {"nodemon": "^3.0.1"}}