# Production Environment Variables for AutoReply Backend

# Database Configuration
MONGODB_URI=mongodb+srv://yousefmuhamedeng22:<EMAIL>/autoreply

# JWT Secret (Strong random string for production)
JWT_SECRET=AutoReply2025SecureJWTKeyForProductionUseOnly!@#$%^&*()

# Session Secret
SESSION_SECRET=AutoReply2025SecureSessionKeyForProductionUseOnly!@#$%^&*()

# Google Gemini AI Configuration
GEMINI_API_KEY=AIzaSyAAPHM_8GBo0ZjY4XWscg-JU6LoSjguHPo

# Facebook Integration (Optional)
FACEBOOK_APP_ID=708787151686025
FACEBOOK_APP_SECRET=********************************
FACEBOOK_VERIFY_TOKEN=autoreply_webhook_verify_token_2025

# Server Configuration
PORT=3000
NODE_ENV=production

# Frontend URL (Firebase Hosting)
FRONTEND_URL=https://elzaeemreblay-1fea2.web.app

# Security Settings
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Logging Level
LOG_LEVEL=info
