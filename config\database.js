const mongoose = require('mongoose');

class Database {
  constructor() {
    this.connection = null;
  }

  async connect() {
    try {
      if (this.connection) {
        return this.connection;
      }

      const mongoUri = process.env.MONGODB_URI;
      if (!mongoUri) {
        throw new Error('MONGODB_URI environment variable is not set');
      }

      this.connection = await mongoose.connect(mongoUri, {
        maxPoolSize: 10,
        serverSelectionTimeoutMS: 5000,
        socketTimeoutMS: 45000,
      });

      // Handle connection events
      mongoose.connection.on('error', (err) => {
        if (process.env.NODE_ENV === 'development') {
          console.error('MongoDB connection error:', err);
        }
      });

      mongoose.connection.on('disconnected', () => {
        this.connection = null;
      });

      return this.connection;
    } catch (error) {
      if (process.env.NODE_ENV === 'development') {
        console.error('MongoDB connection failed:', error);
      }
      this.connection = null;
      throw error;
    }
  }

  async disconnect() {
    try {
      if (this.connection) {
        await mongoose.disconnect();
        this.connection = null;
      }
    } catch (error) {
      if (process.env.NODE_ENV === 'development') {
        console.error('Error disconnecting from MongoDB:', error);
      }
      throw error;
    }
  }

  isConnected() {
    return mongoose.connection.readyState === 1;
  }

  getConnection() {
    return this.connection;
  }
}

module.exports = new Database();
