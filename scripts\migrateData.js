require('dotenv').config();
const fs = require('fs-extra');
const path = require('path');
const database = require('../config/database');

// Import models
const User = require('../models/User');
const Token = require('../models/Token');
const Message = require('../models/Message');
const Product = require('../models/Product');
const StoreInfo = require('../models/StoreInfo');
const Order = require('../models/Order');
const ActivationCode = require('../models/ActivationCode');

const DATA_DIR = path.join(__dirname, '..', 'data');

async function migrateUsers() {
  console.log('Migrating users...');
  try {
    const usersFile = path.join(DATA_DIR, 'users.json');
    if (!fs.existsSync(usersFile)) {
      console.log('No users.json file found, skipping...');
      return;
    }

    const users = fs.readJsonSync(usersFile);
    console.log(`Found ${users.length} users to migrate`);

    for (const userData of users) {
      try {
        // Check if user already exists
        const existingUser = await User.findById(userData.id);
        if (existingUser) {
          console.log(`User ${userData.username} already exists, skipping...`);
          continue;
        }

        // Create new user with the original ID
        const user = new User({
          _id: userData.id,
          username: userData.username,
          password: userData.password,
          email: userData.email,
          name: userData.name,
          messageCount: userData.messageCount || 0,
          freeMessagesRemaining: userData.freeMessagesRemaining || 50,
          activationCode: userData.activationCode,
          activationExpiry: userData.activationExpiry ? new Date(userData.activationExpiry) : null,
          activationType: userData.activationType || 'temp',
          createdAt: userData.createdAt ? new Date(userData.createdAt) : new Date(),
          updatedAt: new Date()
        });

        await user.save();
        console.log(`Migrated user: ${userData.username}`);
      } catch (error) {
        console.error(`Error migrating user ${userData.username}:`, error.message);
      }
    }
  } catch (error) {
    console.error('Error migrating users:', error);
  }
}

async function migrateTokens() {
  console.log('Migrating tokens...');
  try {
    const tokensFile = path.join(DATA_DIR, 'tokens.json');
    if (!fs.existsSync(tokensFile)) {
      console.log('No tokens.json file found, skipping...');
      return;
    }

    const tokens = fs.readJsonSync(tokensFile);
    console.log(`Found ${tokens.length} tokens to migrate`);

    for (const tokenData of tokens) {
      try {
        // Check if token already exists
        const existingToken = await Token.findOne({ userId: tokenData.userId });
        if (existingToken) {
          console.log(`Token for user ${tokenData.userId} already exists, skipping...`);
          continue;
        }

        const token = new Token({
          userId: tokenData.userId,
          facebookId: tokenData.facebookId,
          name: tokenData.name,
          email: tokenData.email,
          accessToken: tokenData.accessToken,
          expiresAt: new Date(tokenData.expiresAt),
          accounts: tokenData.accounts || [],
          connectedAt: tokenData.connectedAt ? new Date(tokenData.connectedAt) : new Date(),
          createdAt: tokenData.connectedAt ? new Date(tokenData.connectedAt) : new Date(),
          updatedAt: new Date()
        });

        await token.save();
        console.log(`Migrated token for user: ${tokenData.userId}`);
      } catch (error) {
        console.error(`Error migrating token for user ${tokenData.userId}:`, error.message);
      }
    }
  } catch (error) {
    console.error('Error migrating tokens:', error);
  }
}

async function migrateMessages() {
  console.log('Migrating messages...');
  try {
    const messagesFile = path.join(DATA_DIR, 'messages.json');
    if (!fs.existsSync(messagesFile)) {
      console.log('No messages.json file found, skipping...');
      return;
    }

    const messages = fs.readJsonSync(messagesFile);
    console.log(`Found ${messages.length} messages to migrate`);

    for (const messageData of messages) {
      try {
        const message = new Message({
          userId: messageData.userId,
          platform: messageData.platform,
          content: messageData.content,
          type: messageData.type,
          metadata: messageData.metadata || {},
          createdAt: messageData.timestamp ? new Date(messageData.timestamp) : new Date(),
          updatedAt: new Date()
        });

        await message.save();
      } catch (error) {
        console.error(`Error migrating message:`, error.message);
      }
    }
    console.log(`Migrated ${messages.length} messages`);
  } catch (error) {
    console.error('Error migrating messages:', error);
  }
}

async function migrateProducts() {
  console.log('Migrating products...');
  try {
    const files = fs.readdirSync(DATA_DIR);
    const productFiles = files.filter(file => file.startsWith('products_') && file.endsWith('.json'));

    console.log(`Found ${productFiles.length} product files to migrate`);

    for (const file of productFiles) {
      const userId = file.replace('products_', '').replace('.json', '');
      const filePath = path.join(DATA_DIR, file);

      try {
        const products = fs.readJsonSync(filePath);
        console.log(`Migrating ${products.length} products for user ${userId}`);

        for (const productData of products) {
          try {
            const product = new Product({
              _id: productData.id,
              userId: userId,
              name: productData.name,
              price: productData.price,
              description: productData.description,
              category: productData.category || '',
              image: productData.image || '',
              inStock: productData.inStock !== undefined ? productData.inStock : true,
              createdAt: productData.createdAt ? new Date(productData.createdAt) : new Date(),
              updatedAt: new Date()
            });

            await product.save();
          } catch (error) {
            console.error(`Error migrating product ${productData.name}:`, error.message);
          }
        }
      } catch (error) {
        console.error(`Error reading product file ${file}:`, error.message);
      }
    }
  } catch (error) {
    console.error('Error migrating products:', error);
  }
}

async function migrateStoreInfo() {
  console.log('Migrating store info...');
  try {
    const files = fs.readdirSync(DATA_DIR);
    const storeFiles = files.filter(file => file.startsWith('store_info_') && file.endsWith('.json'));

    console.log(`Found ${storeFiles.length} store info files to migrate`);

    for (const file of storeFiles) {
      const userId = file.replace('store_info_', '').replace('.json', '');
      const filePath = path.join(DATA_DIR, file);

      try {
        const storeData = fs.readJsonSync(filePath);

        const storeInfo = new StoreInfo({
          userId: userId,
          name: storeData.name || '',
          address: storeData.address || '',
          description: storeData.description || '',
          phone: storeData.phone || '',
          email: storeData.email || '',
          website: storeData.website || '',
          logo: storeData.logo || '',
          createdAt: storeData.updatedAt ? new Date(storeData.updatedAt) : new Date(),
          updatedAt: new Date()
        });

        await storeInfo.save();
        console.log(`Migrated store info for user ${userId}`);
      } catch (error) {
        console.error(`Error migrating store info for user ${userId}:`, error.message);
      }
    }
  } catch (error) {
    console.error('Error migrating store info:', error);
  }
}

async function migrateOrders() {
  console.log('Migrating orders...');
  try {
    const files = fs.readdirSync(DATA_DIR);
    const orderFiles = files.filter(file => file.startsWith('orders_') && file.endsWith('.json'));

    console.log(`Found ${orderFiles.length} order files to migrate`);

    for (const file of orderFiles) {
      const userId = file.replace('orders_', '').replace('.json', '');
      const filePath = path.join(DATA_DIR, file);

      try {
        const orders = fs.readJsonSync(filePath);
        console.log(`Migrating ${orders.length} orders for user ${userId}`);

        for (const orderData of orders) {
          try {
            const order = new Order({
              _id: orderData.id,
              userId: userId,
              customerName: orderData.customerName || '',
              customerPhone: orderData.customerPhone || '',
              customerAddress: orderData.customerAddress || '',
              items: orderData.items || [],
              totalAmount: orderData.totalAmount || '0',
              status: orderData.status || 'pending',
              notes: orderData.notes || '',
              paymentMethod: orderData.paymentMethod || 'cash',
              createdAt: orderData.createdAt ? new Date(orderData.createdAt) : new Date(),
              updatedAt: orderData.updatedAt ? new Date(orderData.updatedAt) : new Date()
            });

            await order.save();
          } catch (error) {
            console.error(`Error migrating order ${orderData.id}:`, error.message);
          }
        }
      } catch (error) {
        console.error(`Error reading order file ${file}:`, error.message);
      }
    }
  } catch (error) {
    console.error('Error migrating orders:', error);
  }
}

async function migrateActivationCodes() {
  console.log('Migrating activation codes...');
  try {
    const codesFile = path.join(DATA_DIR, 'activation_codes.json');
    if (!fs.existsSync(codesFile)) {
      console.log('No activation_codes.json file found, skipping...');
      return;
    }

    const codes = fs.readJsonSync(codesFile);
    console.log(`Found ${codes.length} activation codes to migrate`);

    for (const codeData of codes) {
      try {
        const activationCode = new ActivationCode({
          code: codeData.code,
          type: codeData.type || 'temp',
          duration: codeData.duration || 30,
          used: codeData.used || false,
          usedBy: codeData.usedBy || null,
          usedAt: codeData.usedAt ? new Date(codeData.usedAt) : null,
          description: codeData.description || '',
          createdAt: codeData.createdAt ? new Date(codeData.createdAt) : new Date(),
          updatedAt: new Date()
        });

        await activationCode.save();
      } catch (error) {
        console.error(`Error migrating activation code ${codeData.code}:`, error.message);
      }
    }
    console.log(`Migrated ${codes.length} activation codes`);
  } catch (error) {
    console.error('Error migrating activation codes:', error);
  }
}

async function main() {
  try {
    console.log('Starting data migration from JSON files to MongoDB...');

    // Connect to database
    await database.connect();
    console.log('Connected to MongoDB');

    // Run migrations in order
    await migrateUsers();
    await migrateTokens();
    await migrateMessages();
    await migrateProducts();
    await migrateStoreInfo();
    await migrateOrders();
    await migrateActivationCodes();

    console.log('Data migration completed successfully!');
  } catch (error) {
    console.error('Migration failed:', error);
  } finally {
    // Disconnect from database
    await database.disconnect();
    console.log('Disconnected from MongoDB');
    process.exit(0);
  }
}

// Run the migration
main();
